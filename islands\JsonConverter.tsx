import { useState } from "preact/hooks";

type ConversionMode = 'format' | 'minify' | 'toString' | 'fromString';

export default function JsonConverter() {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [mode, setMode] = useState<ConversionMode>('format');
  const [error, setError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleConvert = async () => {
    if (!input.trim()) {
      setError('请输入要转换的内容');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      const response = await fetch('/api/json-convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: input.trim(),
          mode
        })
      });

      const result = await response.json();

      if (result.success) {
        setOutput(result.output);
      } else {
        setError(result.error || '转换失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleCopy = async () => {
    if (output) {
      try {
        await navigator.clipboard.writeText(output);
        // Show success message (you could add a toast notification here)
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  const handleDownload = () => {
    if (output) {
      const blob = new Blob([output], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `converted-${mode}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const modeOptions = [
    { value: 'format', label: '格式化 JSON', description: '美化 JSON 格式' },
    { value: 'minify', label: '压缩 JSON', description: '移除空格和换行' },
    { value: 'toString', label: 'JSON 转字符串', description: '转换为转义字符串' },
    { value: 'fromString', label: '字符串转 JSON', description: '解析字符串为 JSON' }
  ];

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Mode Selection */}
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-3">
          选择转换模式
        </label>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          {modeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setMode(option.value as ConversionMode)}
              class={`p-3 rounded-lg border-2 text-left transition-all ${
                mode === option.value
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div class="font-medium text-sm">{option.label}</div>
              <div class="text-xs text-gray-500 mt-1">{option.description}</div>
            </button>
          ))}
        </div>
      </div>

      <div class="grid lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            输入内容
          </label>
          <textarea
            value={input}
            onInput={(e) => setInput((e.target as HTMLTextAreaElement).value)}
            placeholder={
              mode === 'fromString' 
                ? '输入要解析的 JSON 字符串...' 
                : '输入 JSON 数据...'
            }
            class="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none json-editor custom-scrollbar"
          />
        </div>

        {/* Output Section */}
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            转换结果
          </label>
          <textarea
            value={output}
            readonly
            placeholder="转换结果将显示在这里..."
            class="w-full h-64 p-4 border border-gray-300 rounded-lg bg-gray-50 resize-none json-editor custom-scrollbar"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div class="mt-6 flex flex-wrap gap-3">
        <button
          onClick={handleConvert}
          disabled={isProcessing}
          class="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors btn-hover-effect"
        >
          {isProcessing && <div class="spinner mr-2"></div>}
          {isProcessing ? '转换中...' : '开始转换'}
        </button>
        
        <button
          onClick={handleClear}
          class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
        >
          清空
        </button>

        {output && (
          <>
            <button
              onClick={handleCopy}
              class="px-6 py-2 bg-success-600 text-white rounded-lg hover:bg-success-700 transition-colors"
            >
              复制结果
            </button>
            
            <button
              onClick={handleDownload}
              class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              下载文件
            </button>
          </>
        )}
      </div>
    </div>
  );
}
