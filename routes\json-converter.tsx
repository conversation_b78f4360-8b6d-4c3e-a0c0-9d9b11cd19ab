import { Head } from "$fresh/runtime.ts";
import { useState } from "preact/hooks";
import Navigation from "../components/Navigation.tsx";
import JsonConverter from "../islands/JsonConverter.tsx";

export default function JsonConverterPage() {
  return (
    <>
      <Head>
        <title>JSON 转换工具 - JSON 与字符串互转</title>
        <meta name="description" content="在线 JSON 转换工具，支持 JSON 格式化、压缩、与字符串互转" />
      </Head>
      
      <div class="min-h-screen bg-gray-50">
        <Navigation />
        
        <main class="container mx-auto px-4 py-8">
          <div class="max-w-6xl mx-auto">
            {/* Header */}
            <div class="text-center mb-8">
              <h1 class="text-3xl font-bold text-gray-900 mb-4">
                🔄 JSON 转换工具
              </h1>
              <p class="text-gray-600 max-w-2xl mx-auto">
                在线 JSON 格式化、压缩和字符串转换工具。支持 JSON 美化、压缩、验证和与字符串的互相转换。
              </p>
            </div>

            {/* Converter Component */}
            <JsonConverter />

            {/* Usage Instructions */}
            <div class="mt-12 bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">使用说明</h2>
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 class="font-medium text-gray-900 mb-2">JSON 格式化</h3>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 将压缩的 JSON 转换为易读格式</li>
                    <li>• 自动验证 JSON 语法</li>
                    <li>• 支持语法高亮显示</li>
                  </ul>
                </div>
                <div>
                  <h3 class="font-medium text-gray-900 mb-2">字符串转换</h3>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• JSON 转换为转义字符串</li>
                    <li>• 字符串解析为 JSON 对象</li>
                    <li>• 支持复制和下载结果</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
