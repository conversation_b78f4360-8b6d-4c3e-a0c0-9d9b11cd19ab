import { Head } from "$fresh/runtime.ts";
import Navigation from "../components/Navigation.tsx";
import FeatureCard from "../components/FeatureCard.tsx";

export default function Home() {
  const features = [
    {
      title: "JSON 转换",
      description: "JSON 与字符串互相转换，支持格式化和压缩",
      icon: "🔄",
      href: "/json-converter",
      color: "primary"
    },
    {
      title: "Word 转 PDF",
      description: "将 Word 文档转换为 PDF 格式",
      icon: "📄",
      href: "/word-to-pdf",
      color: "success"
    },
    {
      title: "PDF 转 Word",
      description: "将 PDF 文档转换为可编辑的 Word 格式",
      icon: "📝",
      href: "/pdf-to-word",
      color: "danger"
    }
  ];

  return (
    <>
      <Head>
        <title>文档转换工具 - 在线转换服务</title>
        <meta name="description" content="免费在线文档转换工具，支持 JSON、Word、PDF 格式转换" />
      </Head>
      
      <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Navigation />
        
        <main class="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-gray-900 mb-6">
              文档转换工具
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
              简单、快速、安全的在线文档转换服务。支持 JSON、Word、PDF 等多种格式转换。
            </p>
          </div>

          {/* Features Grid */}
          <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {features.map((feature) => (
              <FeatureCard key={feature.href} {...feature} />
            ))}
          </div>

          {/* Additional Info */}
          <div class="mt-16 text-center">
            <div class="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
              <h2 class="text-2xl font-semibold text-gray-900 mb-4">
                为什么选择我们？
              </h2>
              <div class="grid md:grid-cols-3 gap-6 text-left">
                <div>
                  <h3 class="font-semibold text-gray-900 mb-2">🚀 快速转换</h3>
                  <p class="text-gray-600">高效的转换算法，几秒钟内完成文档转换</p>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-2">🔒 安全可靠</h3>
                  <p class="text-gray-600">文件处理完成后自动删除，保护您的隐私</p>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-2">💻 免费使用</h3>
                  <p class="text-gray-600">完全免费的在线服务，无需注册或下载</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
