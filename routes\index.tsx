import { Head } from "$fresh/runtime.ts";
import Navigation from "../components/Navigation.tsx";
import FeatureCard from "../components/FeatureCard.tsx";

export default function Home() {
  const features = [
    {
      title: "JSON 转换",
      description: "🆓 免费 JSON 与字符串互相转换，支持格式化和压缩",
      icon: "🔄",
      href: "/json-converter",
      color: "primary" as const
    },
    {
      title: "Word 转 PDF",
      description: "🆓 免费将 Word 文档转换为 PDF 格式",
      icon: "📄",
      href: "/word-to-pdf",
      color: "success" as const
    },
    {
      title: "PDF 转 Word",
      description: "🆓 免费将 PDF 文档转换为可编辑的 Word 格式",
      icon: "📝",
      href: "/pdf-to-word",
      color: "danger" as const
    }
  ];

  return (
    <>
      <Head>
        <title>免费文档转换工具 - 100%免费在线转换服务</title>
        <meta name="description" content="完全免费的在线文档转换工具，支持 JSON、Word、PDF 格式转换，无需注册，无需付费" />
      </Head>
      
      <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Navigation />
        
        <main class="container mx-auto px-4 py-12">
          {/* Hero Section */}
          <div class="text-center mb-16">
            {/* Free Badge */}
            <div class="inline-flex items-center px-6 py-3 rounded-full bg-green-100 border-2 border-green-300 text-green-800 text-lg font-bold mb-6 shadow-lg">
              <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              🎉 100% 完全免费使用
            </div>

            <h1 class="text-5xl font-bold text-gray-900 mb-6">
              <span class="block">免费文档转换工具</span>
              <span class="block text-2xl text-green-600 font-semibold mt-2">永久免费 · 无需注册 · 即用即走</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
              <span class="bg-yellow-100 px-3 py-1 rounded-full text-yellow-800 font-semibold">零成本</span>
              简单、快速、安全的在线文档转换服务。支持 JSON、Word、PDF 等多种格式转换。
            </p>
          </div>

          {/* Features Grid */}
          <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {features.map((feature) => (
              <FeatureCard key={feature.href} {...feature} />
            ))}
          </div>

          {/* Update Notice */}
          <div class="mt-12 max-w-4xl mx-auto">
            <div class="bg-green-50 border-2 border-green-200 rounded-lg p-6">
              <div class="flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <h3 class="text-2xl font-bold text-green-800">✅ 乱码问题已修复！</h3>
              </div>
              <div class="text-green-700 space-y-2">
                <p class="text-lg"><strong>最新更新：</strong>文档转换功能不再出现乱码，现在提供清晰的中文说明和专业建议。</p>
                <div class="grid md:grid-cols-2 gap-4 mt-4">
                  <div class="bg-white p-4 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">📄 Word 转 PDF</h4>
                    <p class="text-sm text-green-700">TXT 文件完整转换，Word 文档显示清晰说明</p>
                  </div>
                  <div class="bg-white p-4 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">📝 PDF 转 Word</h4>
                    <p class="text-sm text-green-700">提供专业技术建议和解决方案指导</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div class="mt-12 text-center">
            <div class="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
              <h2 class="text-3xl font-bold text-gray-900 mb-6">
                <span class="text-green-600">💰 100% 免费</span> - 为什么选择我们？
              </h2>
              <div class="grid md:grid-cols-3 gap-6 text-left">
                <div class="bg-green-50 p-4 rounded-lg border-2 border-green-200">
                  <h3 class="font-bold text-green-800 mb-2 text-lg">💰 永久免费</h3>
                  <p class="text-green-700">完全免费的在线服务，无需注册、无需付费、无隐藏费用</p>
                  <div class="mt-2 text-sm text-green-600 font-semibold">✓ 无限次使用</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg border-2 border-blue-200">
                  <h3 class="font-bold text-blue-800 mb-2 text-lg">🚀 快速转换</h3>
                  <p class="text-blue-700">高效的转换算法，几秒钟内完成文档转换</p>
                  <div class="mt-2 text-sm text-blue-600 font-semibold">✓ 即时处理</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border-2 border-purple-200">
                  <h3 class="font-bold text-purple-800 mb-2 text-lg">🔒 安全可靠</h3>
                  <p class="text-purple-700">文件处理完成后自动删除，保护您的隐私</p>
                  <div class="mt-2 text-sm text-purple-600 font-semibold">✓ 隐私保护</div>
                </div>
              </div>

              {/* Free Emphasis */}
              <div class="mt-8 bg-gradient-to-r from-green-400 to-blue-500 text-white p-6 rounded-lg">
                <h3 class="text-2xl font-bold mb-2">🎉 完全免费，永远免费！</h3>
                <p class="text-lg">无需注册账号，无需信用卡，无需下载软件 - 打开网页即可使用</p>
                <div class="flex flex-wrap gap-4 mt-4 text-sm">
                  <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">✓ 无注册要求</span>
                  <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">✓ 无付费计划</span>
                  <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">✓ 无使用限制</span>
                  <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">✓ 无广告干扰</span>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
