// Simple test script to verify API functionality
// Run with: deno run --allow-net --allow-read test_api.js

async function testJsonConverter() {
  console.log('🔄 Testing JSON Converter API...');
  
  const testData = {
    input: '{"name":"测试","age":25,"city":"北京"}',
    mode: 'format'
  };

  try {
    const response = await fetch('http://localhost:8000/api/json-convert', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ JSON Converter API working correctly');
      console.log('📄 Formatted output:', result.output);
    } else {
      console.log('❌ JSON Converter API failed:', result.error);
    }
  } catch (error) {
    console.log('❌ JSON Converter API error:', error.message);
  }
}

async function testFileConverter() {
  console.log('\n📄 Testing File Converter API...');
  
  // Create a simple text file for testing
  const testContent = '这是一个测试文档\n\n用于验证文件转换功能。\n\n包含中文内容和多行文本。';
  const blob = new Blob([testContent], { type: 'text/plain' });
  const file = new File([blob], 'test.txt', { type: 'text/plain' });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('conversionType', 'word-to-pdf');

  try {
    const response = await fetch('http://localhost:8000/api/file-convert', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      
      console.log('✅ File Converter API working correctly');
      console.log('📄 Response content type:', contentType);
      console.log('📏 Response content length:', contentLength, 'bytes');
      
      // Save the response to verify it's a valid PDF
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Check if it starts with PDF header
      const pdfHeader = '%PDF';
      const headerBytes = new TextDecoder().decode(uint8Array.slice(0, 4));
      
      if (headerBytes === pdfHeader) {
        console.log('✅ Generated file appears to be a valid PDF');
      } else {
        console.log('⚠️ Generated file may not be a valid PDF');
      }
      
    } else {
      const errorData = await response.json();
      console.log('❌ File Converter API failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ File Converter API error:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting API Tests...\n');
  
  await testJsonConverter();
  await testFileConverter();
  
  console.log('\n✨ Tests completed!');
}

// Run tests if this script is executed directly
if (import.meta.main) {
  runTests();
}
