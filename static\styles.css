/* Custom styles for the document converter */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom file upload styles */
.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.file-upload-area.dragover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Loading spinner */
.spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  transition: width 0.3s ease;
}

/* JSON editor styles */
.json-editor {
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Button hover effects */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Card animations */
.card-animate {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-text-sm {
    font-size: 0.875rem;
  }
  
  .mobile-p-4 {
    padding: 1rem;
  }
}
