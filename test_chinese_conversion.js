// Test script for Chinese text conversion
// Run with: deno run --allow-net --allow-read --allow-write test_chinese_conversion.js

async function testChineseWordToPdf() {
  console.log('🔄 Testing Chinese Word to PDF conversion...');
  
  // Create a Chinese text file
  const chineseText = `测试文档

这是一个包含中文内容的测试文档。

主要内容：
1. 中文字符测试：你好世界
2. 标点符号：，。！？；：
3. 数字和英文：123 ABC test
4. 特殊符号：@#$%^&*()

段落测试：
这是第一段文字，包含了常用的中文字符。
这是第二段文字，用来测试换行和格式。

列表测试：
• 项目一
• 项目二  
• 项目三

结论：
如果这些中文字符能够正确显示在PDF中，
说明转换功能工作正常。

测试时间：${new Date().toLocaleString('zh-CN')}`;

  const blob = new Blob([chineseText], { type: 'text/plain' });
  const file = new File([blob], 'chinese_test.txt', { type: 'text/plain' });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('conversionType', 'word-to-pdf');

  try {
    const response = await fetch('http://localhost:8000/api/file-convert', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      console.log('✅ Word to PDF conversion successful');
      console.log('📏 Generated PDF size:', uint8Array.length, 'bytes');
      
      // Check if it's a valid PDF
      const pdfHeader = '%PDF';
      const headerBytes = new TextDecoder().decode(uint8Array.slice(0, 4));
      
      if (headerBytes === pdfHeader) {
        console.log('✅ Generated file is a valid PDF');
        
        // Save the PDF for manual inspection
        await Deno.writeFile('chinese_test_output.pdf', uint8Array);
        console.log('📁 Saved PDF to chinese_test_output.pdf');
        
        // Check PDF content for encoding issues
        const pdfContent = new TextDecoder('utf-8', { ignoreBOM: true, fatal: false }).decode(uint8Array);
        
        if (pdfContent.includes('Chinese text:') || pdfContent.includes('[Chinese')) {
          console.log('✅ PDF uses safe encoding for Chinese text');
        } else if (pdfContent.includes('测试') || pdfContent.includes('中文')) {
          console.log('✅ PDF contains Chinese characters directly');
        } else {
          console.log('⚠️ PDF content encoding unclear');
        }
        
      } else {
        console.log('❌ Generated file is not a valid PDF');
        console.log('📄 File header:', headerBytes);
      }
      
    } else {
      const errorData = await response.json();
      console.log('❌ Conversion failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

async function testChinesePdfToWord() {
  console.log('\n🔄 Testing Chinese PDF to Word conversion...');
  
  // Create a simple PDF with Chinese content reference
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 50
>>
stream
BT
/F1 12 Tf
50 750 Td
(Test PDF with Chinese content) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000274 00000 n
0000000380 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
430
%%EOF`;

  const blob = new Blob([pdfContent], { type: 'application/pdf' });
  const file = new File([blob], 'chinese_test.pdf', { type: 'application/pdf' });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('conversionType', 'pdf-to-word');

  try {
    const response = await fetch('http://localhost:8000/api/file-convert', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      console.log('✅ PDF to Word conversion successful');
      console.log('📏 Generated file size:', uint8Array.length, 'bytes');
      
      // Save the file for manual testing
      await Deno.writeFile('chinese_pdf_to_word_output.docx', uint8Array);
      console.log('📁 Saved Word file to chinese_pdf_to_word_output.docx');
      
      // Check if it's readable text
      const textContent = new TextDecoder('utf-8').decode(uint8Array);
      
      if (textContent.includes('PDF 转 Word') && textContent.includes('转换结果')) {
        console.log('✅ Generated file contains proper Chinese content');
        console.log('💡 Try opening chinese_pdf_to_word_output.docx with Word');
        
        // Also save as .txt for easier inspection
        await Deno.writeFile('chinese_pdf_to_word_output.txt', uint8Array);
        console.log('📁 Also saved as .txt file for inspection');
        
      } else {
        console.log('⚠️ Generated file content unclear');
        console.log('📄 First 200 characters:', textContent.substring(0, 200));
      }
      
    } else {
      const errorData = await response.json();
      console.log('❌ Conversion failed:', errorData.error);
    }
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

async function runChineseConversionTests() {
  console.log('🧪 Starting Chinese Text Conversion Tests...\n');
  
  await testChineseWordToPdf();
  await testChinesePdfToWord();
  
  console.log('\n✨ Chinese conversion tests completed!');
  console.log('\n📋 Files generated for manual testing:');
  console.log('• chinese_test_output.pdf - Word to PDF result');
  console.log('• chinese_pdf_to_word_output.docx - PDF to Word result');
  console.log('• chinese_pdf_to_word_output.txt - Same content as text file');
  console.log('\n💡 Manual testing steps:');
  console.log('1. Open chinese_test_output.pdf with a PDF viewer');
  console.log('2. Try opening chinese_pdf_to_word_output.docx with Word');
  console.log('3. If Word fails, check the .txt version');
  console.log('4. Verify Chinese characters display correctly');
}

// Run tests if this script is executed directly
if (import.meta.main) {
  runChineseConversionTests();
}
