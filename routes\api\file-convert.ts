import { HandlerContext } from "$fresh/server.ts";

export const handler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const conversionType = formData.get('conversionType') as string;

    if (!file || !conversionType) {
      return new Response(JSON.stringify({ error: '缺少必要参数' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file size (max 15MB)
    const maxSize = 15 * 1024 * 1024;
    if (file.size > maxSize) {
      return new Response(JSON.stringify({ error: '文件大小超过限制' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // For demo purposes, we'll create a mock conversion
    // In a real implementation, you would use libraries like:
    // - LibreOffice headless for Word to PDF conversion
    // - pdf2pic + OCR for PDF to Word conversion
    // - Or cloud services like Google Docs API, Microsoft Graph API

    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing time

    let outputBuffer: Uint8Array;
    let outputFilename: string;
    let contentType: string;

    if (conversionType === 'word-to-pdf') {
      // Mock PDF creation
      outputBuffer = await createMockPdf(file.name);
      outputFilename = file.name.replace(/\.(doc|docx)$/i, '.pdf');
      contentType = 'application/pdf';
    } else if (conversionType === 'pdf-to-word') {
      // Mock DOCX creation
      outputBuffer = await createMockDocx(file.name);
      outputFilename = file.name.replace(/\.pdf$/i, '.docx');
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else {
      return new Response(JSON.stringify({ error: '不支持的转换类型' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(outputBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${outputFilename}"`,
        'Content-Length': outputBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('File conversion error:', error);
    return new Response(JSON.stringify({ error: '转换失败，请重试' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};

// Mock PDF creation function
async function createMockPdf(originalFilename: string): Promise<Uint8Array> {
  // This is a minimal PDF structure for demonstration
  // In a real implementation, you would use proper PDF generation libraries
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 100
>>
stream
BT
/F1 12 Tf
50 750 Td
(Converted from: ${originalFilename}) Tj
0 -20 Td
(This is a demo conversion.) Tj
0 -20 Td
(In production, use proper conversion libraries.) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000424 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
521
%%EOF`;

  return new TextEncoder().encode(pdfContent);
}

// Mock DOCX creation function
async function createMockDocx(originalFilename: string): Promise<Uint8Array> {
  // This creates a minimal DOCX file structure
  // In a real implementation, you would use proper DOCX generation libraries
  
  // For demo purposes, we'll create a simple text file with DOCX extension
  // Real DOCX files are ZIP archives with XML content
  const docxContent = `Converted from: ${originalFilename}

This is a demo conversion from PDF to Word.

In a production environment, you would use proper document conversion libraries such as:
- LibreOffice headless mode
- Apache POI for Java
- python-docx for Python
- Or cloud-based conversion services

The converted document would maintain the original formatting, images, and layout as much as possible.`;

  return new TextEncoder().encode(docxContent);
}
