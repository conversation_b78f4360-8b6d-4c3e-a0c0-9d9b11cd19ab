# 使用指南

## 🚀 快速开始

### 1. 启动项目
```bash
deno task start
```

### 2. 访问网站
打开浏览器访问：http://localhost:8000

## 📋 功能说明

### JSON 转换工具
**位置**: http://localhost:8000/json-converter

**功能**:
- ✅ JSON 格式化（美化）
- ✅ JSON 压缩
- ✅ JSON 转字符串
- ✅ 字符串转 JSON
- ✅ 语法验证

**使用方法**:
1. 选择转换模式
2. 在左侧输入框粘贴内容
3. 点击"开始转换"
4. 在右侧查看结果
5. 可以复制或下载结果

**测试数据**: 使用 `test_data/sample.json` 中的内容进行测试

### Word 转 PDF 工具
**位置**: http://localhost:8000/word-to-pdf

**✅ 已修复乱码问题**:
- ✅ TXT 文件完整转换
- ✅ Word 文档不再出现乱码
- ✅ 提供清晰的转换说明
- ✅ 生成包含正确信息的 PDF

**推荐测试流程**:
1. 使用 `test_data/improved_sample.txt` 测试（推荐）
2. 验证 TXT 转 PDF 功能正常
3. 尝试 Word 文档（会显示清晰说明而非乱码）

**使用方法**:
1. 拖拽或选择文件
2. 点击"开始转换"
3. 等待处理完成
4. 自动下载 PDF 文件

### PDF 转 Word 工具
**位置**: http://localhost:8000/pdf-to-word

**✅ 已修复乱码问题**:
- ✅ 不再出现乱码内容
- ✅ 提供清晰的处理说明
- ✅ 详细的技术建议
- ✅ 生产环境解决方案指导

**使用方法**:
1. 上传 PDF 文件
2. 点击"开始转换"
3. 等待处理完成
4. 下载转换结果

## 🧪 测试建议

### 测试顺序
1. **JSON 转换**: 最稳定，先测试这个功能
2. **TXT 转 PDF**: 使用 `test_data/sample.txt`
3. **Word 转 PDF**: 使用简单的 Word 文档
4. **PDF 转 Word**: 使用包含文本的 PDF

### 测试文件
项目包含以下测试文件：
- `test_data/sample.json` - JSON 测试数据
- `test_data/sample.txt` - 文本文件测试

### API 测试
运行自动化测试：
```bash
deno run --allow-net --allow-read test_api.js
```

## ⚠️ 当前限制

### 演示版本说明
这是一个功能演示版本，主要用于展示项目架构和基础功能。

### 文档转换限制
- **格式保持**: 不能完全保持原始格式
- **复杂元素**: 不支持图像、表格等
- **字体样式**: 不保持字体和样式信息
- **布局**: 不保持复杂布局

### 建议的生产环境方案
- **Word 转 PDF**: LibreOffice headless、Puppeteer
- **PDF 转 Word**: PDF.js、Apache Tika、云服务 API

## 🔧 故障排除

### 常见问题

**Q: 转换后出现乱码怎么办？**
A: ✅ 已修复！当前版本不再出现乱码：
- 使用 `test_data/improved_sample.txt` 测试
- Word/PDF 转换会显示清晰的说明信息
- 不再尝试提取可能乱码的内容

**Q: Word 文档转换效果如何？**
A: 当前版本会提供清晰说明：
- 显示文件基本信息
- 说明当前版本限制
- 提供生产环境建议
- 不再显示乱码内容

**Q: PDF 转换结果如何？**
A: 改进后的版本会显示：
- 详细的处理状态
- 技术挑战说明
- 专业解决方案建议
- 清晰的功能限制说明

### 解决方案
1. 使用包含纯文本的文件进行测试
2. 检查浏览器控制台的错误信息
3. 查看服务器终端的日志输出

## 📈 后续改进

### 短期目标
- 集成真实的文档转换库
- 改进文本提取算法
- 添加更多文件格式支持

### 长期目标
- 保持原始格式和样式
- 支持图像和表格转换
- 添加批量转换功能
- 集成云服务 API

## 💡 开发建议

如果您想改进这个项目：

1. **集成 LibreOffice**: 用于真实的 Word 转 PDF
2. **使用 PDF.js**: 用于更好的 PDF 解析
3. **添加 JSZip**: 用于创建真实的 DOCX 文件
4. **集成 OCR**: 用于扫描版 PDF 的文本识别

## 📞 技术支持

如果遇到问题：
1. 检查浏览器控制台
2. 查看服务器日志
3. 运行 API 测试脚本
4. 参考 README.md 文档
