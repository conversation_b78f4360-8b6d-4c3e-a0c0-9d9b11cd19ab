# 使用指南

## 🚀 快速开始

### 1. 启动项目
```bash
deno task start
```

### 2. 访问网站
打开浏览器访问：http://localhost:8000

## 📋 功能说明

### JSON 转换工具
**位置**: http://localhost:8000/json-converter

**功能**:
- ✅ JSON 格式化（美化）
- ✅ JSON 压缩
- ✅ JSON 转字符串
- ✅ 字符串转 JSON
- ✅ 语法验证

**使用方法**:
1. 选择转换模式
2. 在左侧输入框粘贴内容
3. 点击"开始转换"
4. 在右侧查看结果
5. 可以复制或下载结果

**测试数据**: 使用 `test_data/sample.json` 中的内容进行测试

### Word 转 PDF 工具
**位置**: http://localhost:8000/word-to-pdf

**当前支持**:
- ✅ TXT 文件完整转换
- ⚠️ DOC/DOCX 基础文本提取
- ✅ 生成包含内容的 PDF

**推荐测试流程**:
1. 先使用 `test_data/sample.txt` 测试
2. 验证 TXT 转 PDF 功能正常
3. 再尝试 Word 文档

**使用方法**:
1. 拖拽或选择文件
2. 点击"开始转换"
3. 等待处理完成
4. 自动下载 PDF 文件

### PDF 转 Word 工具
**位置**: http://localhost:8000/pdf-to-word

**当前支持**:
- ⚠️ 基础文本提取
- ✅ 生成包含提取内容的文档
- ✅ 文件信息说明

**使用方法**:
1. 上传 PDF 文件
2. 点击"开始转换"
3. 等待处理完成
4. 下载转换结果

## 🧪 测试建议

### 测试顺序
1. **JSON 转换**: 最稳定，先测试这个功能
2. **TXT 转 PDF**: 使用 `test_data/sample.txt`
3. **Word 转 PDF**: 使用简单的 Word 文档
4. **PDF 转 Word**: 使用包含文本的 PDF

### 测试文件
项目包含以下测试文件：
- `test_data/sample.json` - JSON 测试数据
- `test_data/sample.txt` - 文本文件测试

### API 测试
运行自动化测试：
```bash
deno run --allow-net --allow-read test_api.js
```

## ⚠️ 当前限制

### 演示版本说明
这是一个功能演示版本，主要用于展示项目架构和基础功能。

### 文档转换限制
- **格式保持**: 不能完全保持原始格式
- **复杂元素**: 不支持图像、表格等
- **字体样式**: 不保持字体和样式信息
- **布局**: 不保持复杂布局

### 建议的生产环境方案
- **Word 转 PDF**: LibreOffice headless、Puppeteer
- **PDF 转 Word**: PDF.js、Apache Tika、云服务 API

## 🔧 故障排除

### 常见问题

**Q: 转换后的文件内容丢失了？**
A: 当前版本仅支持基础文本提取，建议：
- 先测试 TXT 文件
- 检查原文件是否包含可提取的文本
- 查看生成文件的内容说明

**Q: Word 文档转换效果不好？**
A: 这是预期的，因为：
- 当前版本为演示版本
- 仅做基础文本提取
- 生产环境需要专业转换库

**Q: PDF 转换没有内容？**
A: 可能原因：
- PDF 是扫描版（图像）
- PDF 使用了特殊编码
- 当前版本的提取算法有限

### 解决方案
1. 使用包含纯文本的文件进行测试
2. 检查浏览器控制台的错误信息
3. 查看服务器终端的日志输出

## 📈 后续改进

### 短期目标
- 集成真实的文档转换库
- 改进文本提取算法
- 添加更多文件格式支持

### 长期目标
- 保持原始格式和样式
- 支持图像和表格转换
- 添加批量转换功能
- 集成云服务 API

## 💡 开发建议

如果您想改进这个项目：

1. **集成 LibreOffice**: 用于真实的 Word 转 PDF
2. **使用 PDF.js**: 用于更好的 PDF 解析
3. **添加 JSZip**: 用于创建真实的 DOCX 文件
4. **集成 OCR**: 用于扫描版 PDF 的文本识别

## 📞 技术支持

如果遇到问题：
1. 检查浏览器控制台
2. 查看服务器日志
3. 运行 API 测试脚本
4. 参考 README.md 文档
