# 文档转换工具

一个基于 Deno Fresh 构建的在线文档转换工具，支持多种格式转换功能。

## 功能特性

### 🔄 JSON 转换
- JSON 格式化（美化）
- JSON 压缩（去除空格）
- JSON 转字符串
- 字符串转 JSON
- 语法验证和错误提示

### 📄 Word 转 PDF（演示版本）
- 支持 TXT、DOC、DOCX 格式
- 提取文本内容并生成 PDF
- 最大文件大小：10MB
- **注意**：当前为演示版本，建议先测试 TXT 文件

### 📝 PDF 转 Word（演示版本）
- 基础 PDF 文本提取
- 生成包含提取内容的文档
- 最大文件大小：15MB
- **注意**：当前为演示版本，仅支持基础文本提取

## 技术栈

- **框架**: Deno Fresh 1.6.8
- **前端**: Preact + TypeScript
- **样式**: Twind (Tailwind CSS)
- **运行时**: Deno 2.0+

## 快速开始

### 环境要求
- Deno 2.0 或更高版本

### 安装和运行

1. 克隆项目
```bash
git clone <repository-url>
cd document-converter
```

2. 启动开发服务器
```bash
deno task start
```

3. 打开浏览器访问 `http://localhost:8000`

### 其他命令

```bash
# 生成 manifest 文件
deno task manifest

# 构建生产版本
deno task build

# 运行生产版本
deno task preview

# 代码检查
deno task check
```

## 项目结构

```
├── components/          # 可复用组件
│   ├── Navigation.tsx   # 导航栏组件
│   └── FeatureCard.tsx  # 功能卡片组件
├── islands/             # 客户端交互组件
│   ├── JsonConverter.tsx    # JSON 转换器
│   └── FileConverter.tsx    # 文件转换器
├── routes/              # 路由页面
│   ├── api/            # API 端点
│   │   ├── json-convert.ts  # JSON 转换 API
│   │   └── file-convert.ts  # 文件转换 API
│   ├── index.tsx       # 首页
│   ├── json-converter.tsx   # JSON 转换页面
│   ├── word-to-pdf.tsx     # Word 转 PDF 页面
│   └── pdf-to-word.tsx     # PDF 转 Word 页面
├── static/             # 静态资源
│   └── styles.css      # 自定义样式
└── test_data/          # 测试数据
    └── sample.json     # 示例 JSON 文件
```

## API 接口

### JSON 转换 API
- **端点**: `POST /api/json-convert`
- **参数**: 
  - `input`: 输入内容
  - `mode`: 转换模式 (`format` | `minify` | `toString` | `fromString`)

### 文件转换 API
- **端点**: `POST /api/file-convert`
- **参数**:
  - `file`: 上传的文件
  - `conversionType`: 转换类型 (`word-to-pdf` | `pdf-to-word`)

## 使用说明

### JSON 转换
1. 访问 `/json-converter` 页面
2. 选择转换模式
3. 在左侧输入框粘贴或输入 JSON 内容
4. 点击"开始转换"按钮
5. 在右侧查看转换结果
6. 可以复制结果或下载为文件

### 文档转换
1. 访问对应的转换页面
2. 点击上传区域或拖拽文件
3. 等待文件验证通过
4. 点击"开始转换"按钮
5. 等待转换完成
6. 自动下载转换后的文件

## 注意事项

### ⚠️ 当前版本限制
**这是一个演示版本，具有以下限制：**

- **Word 转 PDF**: 仅支持基础文本提取，推荐先测试 `.txt` 文件
- **PDF 转 Word**: 仅支持简单的文本提取，复杂格式暂不支持
- **格式保持**: 当前版本不能完全保持原始文档的格式和样式
- **图像处理**: 不支持图像、表格等复杂元素的转换

### 文件大小限制
- Word 文档：最大 10MB
- PDF 文档：最大 15MB

### 支持格式
- 文档转 PDF: `.txt`, `.doc`, `.docx`
- PDF 转文档: `.pdf`

### 隐私保护
- 所有文件处理完成后自动删除
- 不会保存用户上传的任何内容
- 转换过程在本地服务器完成

## 开发说明

### 文档转换实现
当前版本使用模拟转换来演示功能。在生产环境中，建议使用以下方案：

**Word 转 PDF:**
- LibreOffice headless 模式
- Puppeteer + HTML 转换
- 云服务 API（如 Microsoft Graph）

**PDF 转 Word:**
- Apache Tika + OCR
- pdf2pic + Tesseract OCR
- 云服务 API（如 Adobe PDF Services）

### 扩展功能
- 支持更多文件格式
- 批量转换功能
- 转换历史记录
- 用户账户系统
- 云存储集成

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
