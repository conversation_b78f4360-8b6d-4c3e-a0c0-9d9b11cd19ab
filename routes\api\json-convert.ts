import { HandlerContext } from "$fresh/server.ts";

interface ConvertRequest {
  input: string;
  mode: 'format' | 'minify' | 'toString' | 'fromString';
}

interface ConvertResponse {
  success: boolean;
  output?: string;
  error?: string;
}

export const handler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ success: false, error: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const { input, mode }: ConvertRequest = await req.json();

    if (!input || !mode) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: '缺少必要参数' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    let output: string;

    switch (mode) {
      case 'format':
        try {
          const parsed = JSON.parse(input);
          output = JSON.stringify(parsed, null, 2);
        } catch (e) {
          return new Response(JSON.stringify({ 
            success: false, 
            error: 'JSON 格式错误: ' + (e as Error).message 
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        break;

      case 'minify':
        try {
          const parsed = JSON.parse(input);
          output = JSON.stringify(parsed);
        } catch (e) {
          return new Response(JSON.stringify({ 
            success: false, 
            error: 'JSON 格式错误: ' + (e as Error).message 
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        break;

      case 'toString':
        try {
          const parsed = JSON.parse(input);
          output = JSON.stringify(JSON.stringify(parsed));
        } catch (e) {
          return new Response(JSON.stringify({ 
            success: false, 
            error: 'JSON 格式错误: ' + (e as Error).message 
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        break;

      case 'fromString':
        try {
          // First parse the string to get the JSON string
          const jsonString = JSON.parse(input);
          if (typeof jsonString !== 'string') {
            throw new Error('输入不是有效的 JSON 字符串');
          }
          // Then parse the JSON string to get the actual object
          const parsed = JSON.parse(jsonString);
          output = JSON.stringify(parsed, null, 2);
        } catch (e) {
          return new Response(JSON.stringify({ 
            success: false, 
            error: '字符串解析错误: ' + (e as Error).message 
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        break;

      default:
        return new Response(JSON.stringify({ 
          success: false, 
          error: '不支持的转换模式' 
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
    }

    const response: ConvertResponse = {
      success: true,
      output
    };

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (e) {
    return new Response(JSON.stringify({ 
      success: false, 
      error: '服务器错误: ' + (e as Error).message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
