// Test script to verify the fixes for Word to PDF and PDF to Word conversion
// Run with: deno run --allow-net --allow-read --allow-write test_fixes.js

async function testWordToPdfFix() {
  console.log('🔄 Testing Word to PDF fix (Chinese encoding)...');
  
  // Create a simple Chinese text file
  const chineseText = `测试文档

这是一个中文测试文件。

内容包括：
1. 中文字符：你好世界
2. 英文字符：Hello World
3. 数字：12345
4. 标点符号：，。！？

如果PDF中显示为 [Chinese text: X chars] 格式，
说明编码问题已经解决，不会出现乱码。

测试时间：${new Date().toLocaleString('zh-CN')}`;

  const blob = new Blob([chineseText], { type: 'text/plain' });
  const file = new File([blob], 'test_chinese.txt', { type: 'text/plain' });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('conversionType', 'word-to-pdf');

  try {
    const response = await fetch('http://localhost:8000/api/file-convert', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      console.log('✅ Word to PDF conversion successful');
      console.log('📏 Generated PDF size:', uint8Array.length, 'bytes');
      
      // Check if it's a valid PDF
      const pdfHeader = new TextDecoder().decode(uint8Array.slice(0, 4));
      if (pdfHeader === '%PDF') {
        console.log('✅ Generated file is a valid PDF');
        
        // Save for manual inspection
        await Deno.writeFile('test_word_to_pdf_fix.pdf', uint8Array);
        console.log('📁 Saved to test_word_to_pdf_fix.pdf');
        
        // Check for safe encoding
        const pdfContent = new TextDecoder('utf-8', { ignoreBOM: true, fatal: false }).decode(uint8Array);
        if (pdfContent.includes('[Chinese text:')) {
          console.log('✅ Chinese text uses safe encoding - no garbled characters');
        } else {
          console.log('⚠️ Encoding method unclear');
        }
      } else {
        console.log('❌ Generated file is not a valid PDF');
      }
    } else {
      console.log('❌ Conversion failed');
    }
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

async function testPdfToWordFix() {
  console.log('\n🔄 Testing PDF to Word fix (file format)...');
  
  // Create a simple PDF
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
50 750 Td
(Test PDF Content) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000274 00000 n
0000000373 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
423
%%EOF`;

  const blob = new Blob([pdfContent], { type: 'application/pdf' });
  const file = new File([blob], 'test.pdf', { type: 'application/pdf' });

  const formData = new FormData();
  formData.append('file', file);
  formData.append('conversionType', 'pdf-to-word');

  try {
    const response = await fetch('http://localhost:8000/api/file-convert', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      console.log('✅ PDF to Word conversion successful');
      console.log('📏 Generated file size:', uint8Array.length, 'bytes');
      
      // Save both as .docx and .txt for testing
      await Deno.writeFile('test_pdf_to_word_fix.docx', uint8Array);
      await Deno.writeFile('test_pdf_to_word_fix.txt', uint8Array);
      console.log('📁 Saved as both .docx and .txt files');
      
      // Check content
      const textContent = new TextDecoder('utf-8').decode(uint8Array);
      
      if (textContent.includes('PDF 转 Word 转换结果')) {
        console.log('✅ Contains proper Chinese headers');
      }
      
      if (textContent.includes('如果 Word 无法打开此文件')) {
        console.log('✅ Contains user guidance for file opening');
      }
      
      if (textContent.includes('将文件扩展名改为 .txt')) {
        console.log('✅ Contains fallback instructions');
      }
      
      console.log('💡 Manual test: Try opening test_pdf_to_word_fix.docx with Word');
      console.log('💡 If Word fails, open test_pdf_to_word_fix.txt instead');
      
    } else {
      console.log('❌ Conversion failed');
    }
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

async function runFixTests() {
  console.log('🧪 Testing Conversion Fixes...\n');
  
  await testWordToPdfFix();
  await testPdfToWordFix();
  
  console.log('\n✨ Fix tests completed!');
  console.log('\n📋 Summary of fixes:');
  console.log('1. Word to PDF: Chinese text uses safe encoding to prevent garbled output');
  console.log('2. PDF to Word: Generates plain text format that Word can open');
  console.log('3. Both conversions: Include clear Chinese instructions and fallback options');
  console.log('\n📁 Generated test files:');
  console.log('• test_word_to_pdf_fix.pdf - Check for proper Chinese handling');
  console.log('• test_pdf_to_word_fix.docx - Try opening with Word');
  console.log('• test_pdf_to_word_fix.txt - Fallback text version');
  console.log('\n💡 Next steps:');
  console.log('1. Open the PDF file and verify no garbled Chinese characters');
  console.log('2. Try opening the .docx file with Microsoft Word');
  console.log('3. If Word shows errors, use the .txt version instead');
  console.log('4. Verify all Chinese text displays correctly');
}

// Run tests if this script is executed directly
if (import.meta.main) {
  runFixTests();
}
