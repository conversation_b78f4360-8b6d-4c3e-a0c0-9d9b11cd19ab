export default function Navigation() {
  return (
    <nav class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <a href="/" class="text-2xl font-bold text-primary-600 hover:text-primary-700">
              📄 转换工具
            </a>
            <span class="ml-3 px-3 py-1 bg-green-100 text-green-800 text-sm font-bold rounded-full border border-green-300">
              🆓 100% 免费
            </span>
          </div>
          
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <a 
                href="/" 
                class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                首页
              </a>
              <a 
                href="/json-converter" 
                class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                JSON 转换
              </a>
              <a 
                href="/word-to-pdf" 
                class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Word 转 PDF
              </a>
              <a 
                href="/pdf-to-word" 
                class="text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                PDF 转 Word
              </a>
            </div>
          </div>

          {/* Mobile menu button */}
          <div class="md:hidden">
            <button 
              type="button" 
              class="text-gray-600 hover:text-primary-600 focus:outline-none focus:text-primary-600"
              onclick="toggleMobileMenu()"
            >
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        <div id="mobile-menu" class="md:hidden hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t">
            <a href="/" class="text-gray-600 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">
              首页
            </a>
            <a href="/json-converter" class="text-gray-600 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">
              JSON 转换
            </a>
            <a href="/word-to-pdf" class="text-gray-600 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">
              Word 转 PDF
            </a>
            <a href="/pdf-to-word" class="text-gray-600 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">
              PDF 转 Word
            </a>
          </div>
        </div>
      </div>

      <script>
        {`
          function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
          }
        `}
      </script>
    </nav>
  );
}
