import { Head } from "$fresh/runtime.ts";
import Navigation from "../components/Navigation.tsx";
import FileConverter from "../islands/FileConverter.tsx";

export default function WordToPdfPage() {
  return (
    <>
      <Head>
        <title>Word 转 PDF - 在线文档转换工具</title>
        <meta name="description" content="免费在线 Word 转 PDF 工具，支持 DOC、DOCX 格式转换为 PDF" />
      </Head>
      
      <div class="min-h-screen bg-gray-50">
        <Navigation />
        
        <main class="container mx-auto px-4 py-8">
          <div class="max-w-4xl mx-auto">
            {/* Header */}
            <div class="text-center mb-8">
              <h1 class="text-3xl font-bold text-gray-900 mb-4">
                📄 Word 转 PDF
              </h1>
              <p class="text-gray-600 max-w-2xl mx-auto">
                将您的 Word 文档（DOC、DOCX）快速转换为 PDF 格式。保持原有格式和布局，支持批量转换。
              </p>
            </div>

            {/* Converter Component */}
            <FileConverter 
              conversionType="word-to-pdf"
              acceptedTypes=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              maxFileSize={10 * 1024 * 1024} // 10MB
            />

            {/* Features */}
            <div class="mt-12 grid md:grid-cols-3 gap-6">
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-success-600 text-2xl mb-3">✅</div>
                <h3 class="font-semibold text-gray-900 mb-2">高质量转换</h3>
                <p class="text-gray-600 text-sm">
                  保持原始文档的格式、字体和布局，确保转换质量
                </p>
              </div>
              
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-primary-600 text-2xl mb-3">🚀</div>
                <h3 class="font-semibold text-gray-900 mb-2">快速处理</h3>
                <p class="text-gray-600 text-sm">
                  先进的转换引擎，几秒钟内完成文档转换
                </p>
              </div>
              
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-danger-600 text-2xl mb-3">🔒</div>
                <h3 class="font-semibold text-gray-900 mb-2">安全可靠</h3>
                <p class="text-gray-600 text-sm">
                  文件处理完成后自动删除，保护您的隐私安全
                </p>
              </div>
            </div>

            {/* Usage Instructions */}
            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">使用说明</h2>
              <div class="space-y-3">
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">1</span>
                  <p class="text-gray-600">点击上传区域或拖拽 Word 文档到指定区域</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">2</span>
                  <p class="text-gray-600">支持 DOC 和 DOCX 格式，文件大小不超过 10MB</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">3</span>
                  <p class="text-gray-600">点击"开始转换"按钮，等待处理完成</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">4</span>
                  <p class="text-gray-600">转换完成后自动下载 PDF 文件</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
