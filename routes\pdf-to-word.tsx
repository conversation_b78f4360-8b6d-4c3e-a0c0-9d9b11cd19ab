import { Head } from "$fresh/runtime.ts";
import Navigation from "../components/Navigation.tsx";
import FileConverter from "../islands/FileConverter.tsx";

export default function PdfToWordPage() {
  return (
    <>
      <Head>
        <title>PDF 转 Word - 在线文档转换工具</title>
        <meta name="description" content="免费在线 PDF 转 Word 工具，将 PDF 文档转换为可编辑的 DOCX 格式" />
      </Head>
      
      <div class="min-h-screen bg-gray-50">
        <Navigation />
        
        <main class="container mx-auto px-4 py-8">
          <div class="max-w-4xl mx-auto">
            {/* Header */}
            <div class="text-center mb-8">
              <h1 class="text-3xl font-bold text-gray-900 mb-4">
                📝 PDF 转 Word
              </h1>
              <p class="text-gray-600 max-w-2xl mx-auto">
                将您的 PDF 文档转换为可编辑的 Word 格式（DOCX）。保持文本格式和布局，方便后续编辑。
              </p>
            </div>

            {/* Converter Component */}
            <FileConverter 
              conversionType="pdf-to-word"
              acceptedTypes=".pdf,application/pdf"
              maxFileSize={15 * 1024 * 1024} // 15MB
            />

            {/* Features */}
            <div class="mt-12 grid md:grid-cols-3 gap-6">
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-danger-600 text-2xl mb-3">📝</div>
                <h3 class="font-semibold text-gray-900 mb-2">可编辑格式</h3>
                <p class="text-gray-600 text-sm">
                  转换为 DOCX 格式，支持文本编辑和格式调整
                </p>
              </div>
              
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-primary-600 text-2xl mb-3">🎯</div>
                <h3 class="font-semibold text-gray-900 mb-2">精准识别</h3>
                <p class="text-gray-600 text-sm">
                  先进的 OCR 技术，准确识别文本和保持格式
                </p>
              </div>
              
              <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="text-success-600 text-2xl mb-3">💾</div>
                <h3 class="font-semibold text-gray-900 mb-2">批量处理</h3>
                <p class="text-gray-600 text-sm">
                  支持多个 PDF 文件同时转换，提高工作效率
                </p>
              </div>
            </div>

            {/* Demo Notice */}
            <div class="mt-8 bg-orange-50 border border-orange-200 rounded-lg p-6">
              <h2 class="text-lg font-semibold text-orange-900 mb-3">📋 演示版本说明</h2>
              <div class="text-orange-800 space-y-2">
                <p>当前版本为功能演示版本，会尝试从 PDF 中提取文本内容并生成 Word 文档。</p>
                <p><strong>支持的功能：</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                  <li>基础 PDF 文本提取</li>
                  <li>生成包含提取内容的文本文档</li>
                  <li>文件信息和转换说明</li>
                </ul>
                <p class="text-sm"><strong>生产环境建议：</strong>使用 PDF.js、Apache Tika 或云服务 API 进行专业转换。</p>
              </div>
            </div>

            {/* Usage Instructions */}
            <div class="mt-6 bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">使用说明</h2>
              <div class="space-y-3">
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-danger-100 text-danger-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">1</span>
                  <p class="text-gray-600">选择或拖拽 PDF 文件到上传区域</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-danger-100 text-danger-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">2</span>
                  <p class="text-gray-600">支持标准 PDF 格式，文件大小不超过 15MB</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-danger-100 text-danger-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">3</span>
                  <p class="text-gray-600">系统尝试提取 PDF 中的文本内容</p>
                </div>
                <div class="flex items-start">
                  <span class="flex-shrink-0 w-6 h-6 bg-danger-100 text-danger-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">4</span>
                  <p class="text-gray-600">转换完成后下载包含提取内容的文档</p>
                </div>
              </div>

              <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 class="font-medium text-yellow-800 mb-2">⚠️ 当前限制</h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                  <li>• 演示版本仅支持基础文本提取</li>
                  <li>• 复杂格式和图像暂不支持</li>
                  <li>• 建议使用包含可选择文本的 PDF</li>
                  <li>• 生产环境需要专业的 PDF 解析库</li>
                </ul>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
