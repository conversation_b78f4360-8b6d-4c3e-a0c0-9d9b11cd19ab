// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_app from "./routes/_app.tsx";
import * as $api_file_convert from "./routes/api/file-convert.ts";
import * as $api_json_convert from "./routes/api/json-convert.ts";
import * as $index from "./routes/index.tsx";
import * as $json_converter from "./routes/json-converter.tsx";
import * as $pdf_to_word from "./routes/pdf-to-word.tsx";
import * as $word_to_pdf from "./routes/word-to-pdf.tsx";
import * as $FileConverter from "./islands/FileConverter.tsx";
import * as $JsonConverter from "./islands/JsonConverter.tsx";
import { type Manifest } from "$fresh/server.ts";

const manifest = {
  routes: {
    "./routes/_app.tsx": $_app,
    "./routes/api/file-convert.ts": $api_file_convert,
    "./routes/api/json-convert.ts": $api_json_convert,
    "./routes/index.tsx": $index,
    "./routes/json-converter.tsx": $json_converter,
    "./routes/pdf-to-word.tsx": $pdf_to_word,
    "./routes/word-to-pdf.tsx": $word_to_pdf,
  },
  islands: {
    "./islands/FileConverter.tsx": $FileConverter,
    "./islands/JsonConverter.tsx": $JsonConverter,
  },
  baseUrl: import.meta.url,
} satisfies Manifest;

export default manifest;
