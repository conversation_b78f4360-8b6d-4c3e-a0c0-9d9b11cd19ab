interface FeatureCardProps {
  title: string;
  description: string;
  icon: string;
  href: string;
  color: 'primary' | 'success' | 'danger';
}

export default function FeatureCard({ title, description, icon, href, color }: FeatureCardProps) {
  const colorClasses = {
    primary: {
      bg: 'bg-primary-50 hover:bg-primary-100',
      border: 'border-primary-200',
      icon: 'text-primary-600',
      title: 'text-primary-900',
      button: 'bg-primary-600 hover:bg-primary-700'
    },
    success: {
      bg: 'bg-success-50 hover:bg-success-100',
      border: 'border-success-200',
      icon: 'text-success-600',
      title: 'text-success-900',
      button: 'bg-success-600 hover:bg-success-700'
    },
    danger: {
      bg: 'bg-danger-50 hover:bg-danger-100',
      border: 'border-danger-200',
      icon: 'text-danger-600',
      title: 'text-danger-900',
      button: 'bg-danger-600 hover:bg-danger-700'
    }
  };

  const classes = colorClasses[color];

  return (
    <div class={`${classes.bg} ${classes.border} border-2 rounded-xl p-8 transition-all duration-300 hover:shadow-lg hover:scale-105`}>
      <div class="text-center">
        <div class={`text-4xl mb-4 ${classes.icon}`}>
          {icon}
        </div>
        <h3 class={`text-xl font-semibold mb-3 ${classes.title}`}>
          {title}
        </h3>
        <p class="text-gray-600 mb-6 leading-relaxed">
          {description}
        </p>
        <a 
          href={href}
          class={`inline-block ${classes.button} text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 hover:shadow-md`}
        >
          开始转换
        </a>
      </div>
    </div>
  );
}
