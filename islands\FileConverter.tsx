import { useState, useRef } from "preact/hooks";

interface FileConverterProps {
  conversionType: 'word-to-pdf' | 'pdf-to-word';
  acceptedTypes: string;
  maxFileSize: number;
}

interface ConversionResult {
  success: boolean;
  downloadUrl?: string;
  filename?: string;
  error?: string;
}

export default function FileConverter({ conversionType, acceptedTypes, maxFileSize }: FileConverterProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<ConversionResult | null>(null);
  const [error, setError] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): boolean => {
    if (file.size > maxFileSize) {
      setError(`文件大小不能超过 ${formatFileSize(maxFileSize)}`);
      return false;
    }

    const allowedTypes = acceptedTypes.split(',').map(type => type.trim());
    const isValidType = allowedTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase());
      }
      return file.type === type;
    });

    if (!isValidType) {
      setError('不支持的文件格式');
      return false;
    }

    return true;
  };

  const handleFileSelect = (file: File) => {
    setError('');
    setResult(null);
    
    if (validateFile(file)) {
      setSelectedFile(file);
    }
  };

  const handleFileInputChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer?.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleConvert = async () => {
    if (!selectedFile) {
      setError('请先选择文件');
      return;
    }

    setIsConverting(true);
    setProgress(0);
    setError('');
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('conversionType', conversionType);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch('/api/file-convert', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const filename = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || 
                         `converted-${Date.now()}.${conversionType === 'word-to-pdf' ? 'pdf' : 'docx'}`;
        
        setResult({
          success: true,
          downloadUrl: url,
          filename
        });
      } else {
        const errorData = await response.json();
        setResult({
          success: false,
          error: errorData.error || '转换失败'
        });
      }
    } catch (err) {
      setResult({
        success: false,
        error: '网络错误，请重试'
      });
    } finally {
      setIsConverting(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const handleDownload = () => {
    if (result?.downloadUrl && result?.filename) {
      const a = document.createElement('a');
      a.href = result.downloadUrl;
      a.download = result.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const handleReset = () => {
    setSelectedFile(null);
    setResult(null);
    setError('');
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* File Upload Area */}
      <div
        class={`file-upload-area p-8 text-center cursor-pointer transition-all ${
          isDragOver ? 'dragover' : ''
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes}
          onChange={handleFileInputChange}
          class="hidden"
        />
        
        <div class="text-4xl mb-4">
          {conversionType === 'word-to-pdf' ? '📄' : '📝'}
        </div>
        
        {selectedFile ? (
          <div>
            <p class="text-lg font-medium text-gray-900 mb-2">
              已选择文件: {selectedFile.name}
            </p>
            <p class="text-sm text-gray-500">
              文件大小: {formatFileSize(selectedFile.size)}
            </p>
          </div>
        ) : (
          <div>
            <p class="text-lg font-medium text-gray-700 mb-2">
              点击选择文件或拖拽文件到此处
            </p>
            <p class="text-sm text-gray-500">
              支持格式: {acceptedTypes.split(',').map(type => type.trim().toUpperCase()).join(', ')}
            </p>
            <p class="text-sm text-gray-500">
              最大文件大小: {formatFileSize(maxFileSize)}
            </p>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {isConverting && (
        <div class="mt-6">
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>转换进度</span>
            <span>{progress}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="progress-bar h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Result */}
      {result && (
        <div class={`mt-4 p-4 rounded-lg ${
          result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          {result.success ? (
            <div>
              <p class="text-green-700 font-medium mb-2">✅ 转换成功！</p>
              <button
                onClick={handleDownload}
                class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                下载文件
              </button>
            </div>
          ) : (
            <p class="text-red-700">{result.error}</p>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div class="mt-6 flex flex-wrap gap-3">
        <button
          onClick={handleConvert}
          disabled={!selectedFile || isConverting}
          class="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors btn-hover-effect"
        >
          {isConverting && <div class="spinner mr-2"></div>}
          {isConverting ? '转换中...' : '开始转换'}
        </button>
        
        <button
          onClick={handleReset}
          class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
        >
          重新选择
        </button>
      </div>
    </div>
  );
}
